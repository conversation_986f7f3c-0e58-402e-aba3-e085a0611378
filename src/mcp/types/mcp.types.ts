import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import OpenAI from 'openai';

export interface MCPServerConfig {
  name: string;
  command: string;
  args?: string[];
  env?: Record<string, string>;
  cwd?: string;
  timeout?: number;
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: any;
  handler: (params: any) => Promise<any>;
  serverId: string;
}

export interface MCPServerInstance {
  id: string;
  config: MCPServerConfig;
  transport?: StdioClientTransport;
  client?: Client;
  status: 'starting' | 'running' | 'stopped' | 'error';
  tools: MCPTool[];
}

// OpenAI-compatible tool type for seamless integration
export type OpenAITool = OpenAI.Chat.Completions.ChatCompletionTool;
export type OpenAIToolCall = OpenAI.Chat.Completions.ChatCompletionMessageToolCall;
