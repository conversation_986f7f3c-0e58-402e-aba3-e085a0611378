version: '3.8'

services:
  # NestJS Application
  nestjs-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - PORT=3000
      - NODE_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE}
      - DATABASE_URL=${DATABASE_URL}
      - CONTEXT_DESCRIPTION_FILE=${CONTEXT_DESCRIPTION_FILE}
      - CONTEXT_SYSTEM_PROMPT_FILE=${CONTEXT_SYSTEM_PROMPT_FILE}
    volumes:
      - ./docs:/app/docs:ro
