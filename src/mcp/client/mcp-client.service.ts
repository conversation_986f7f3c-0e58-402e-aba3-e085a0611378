import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ListToolsResult, CallToolResult } from '@modelcontextprotocol/sdk/types.js';
import { MCPClientRegistry } from './mcp-client.registry';
import { MCPServerRegistry } from '../server/mcp-server.registry';
import { MCPServerService } from '../server/mcp-server.service';
import { MCPTool, OpenAITool, OpenAIToolCall } from '../types/mcp.types';

@Injectable()
export class MCPClientService implements OnModuleInit {
  private readonly logger = new Logger(MCPClientService.name);

  constructor(
    private readonly clientRegistry: MCPClientRegistry,
    private readonly serverRegistry: MCPServerRegistry,
    private readonly serverService: MCPServerService,
  ) {}

  async onModuleInit() {
    this.logger.log('Initializing MCP Client Service');
    // Wait a bit for servers to start up
    setTimeout(() => this.discoverAllTools(), 2000);
  }

  async discoverToolsFromServer(serverId: string): Promise<void> {
    const client = this.serverService.getServerClient(serverId);
    if (!client) {
      this.logger.error(`No client available for server: ${serverId}`);
      return;
    }

    try {
      // List available tools from the server
      const toolsResult: ListToolsResult = await client.listTools();

      // Clear existing tools for this server
      this.clientRegistry.removeToolsByServer(serverId);

      // Register new tools
      for (const toolInfo of toolsResult.tools) {
        const tool: MCPTool = {
          name: toolInfo.name,
          description: toolInfo.description,
          inputSchema: toolInfo.inputSchema,
          serverId,
          handler: async (params: any) => {
            return await this.callTool(serverId, toolInfo.name, params);
          },
        };

        this.clientRegistry.registerTool(tool);
      }

      this.logger.log(`Discovered ${toolsResult.tools.length} tools from server: ${serverId}`);
    } catch (error) {
      this.logger.error(`Failed to discover tools from server ${serverId}:`, error);
    }
  }

  async callTool(serverId: string, toolName: string, params: any): Promise<any> {
    const client = this.serverService.getServerClient(serverId);
    if (!client) {
      throw new Error(`No client available for server: ${serverId}`);
    }

    try {
      const result: CallToolResult = await client.callTool({
        name: toolName,
        arguments: params,
      });

      return result.content;
    } catch (error) {
      this.logger.error(`Error calling tool ${toolName} on server ${serverId}:`, error);
      throw error;
    }
  }

  async executeToolFunction(toolName: string, params: any): Promise<any> {
    const tool = this.clientRegistry.getTool(toolName);
    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    try {
      return await tool.handler(params);
    } catch (error) {
      this.logger.error(`Error executing tool ${toolName}:`, error);
      throw error;
    }
  }

  // OpenAI-compatible method to execute tool calls
  async executeToolCall(toolCall: OpenAIToolCall): Promise<string> {
    this.logger.log(`Executing tool: ${toolCall.function.name} with arguments: ${toolCall.function.arguments}`);

    try {
      const startTime = Date.now();
      const args = JSON.parse(toolCall.function.arguments);
      
      const result = await this.executeToolFunction(toolCall.function.name, args);
      
      const duration = Date.now() - startTime;
      this.logger.log(`Tool ${toolCall.function.name} executed in ${duration}ms`);

      // Convert result to string format expected by OpenAI
      if (Array.isArray(result)) {
        return result
          .filter((item: any) => item.type === 'text')
          .map((item: any) => item.text)
          .join('\n');
      }
      
      return String(result);
    } catch (error) {
      this.logger.error(`Tool execution failed for ${toolCall.function.name}: ${error.message}`, error.stack);
      return `Tool execution failed: ${error.message}`;
    }
  }

  // Get tools in OpenAI-compatible format
  getAvailableTools(): OpenAITool[] {
    const tools = this.clientRegistry.getOpenAITools();
    this.logger.log(`Providing ${tools.length} available MCP tools`);
    this.logger.log(`Available tools: ${tools.map(t => t.function.name).join(', ')}`);
    return tools;
  }

  async refreshTools(): Promise<void> {
    const servers = this.serverRegistry.getRunningServers();
    
    this.logger.log(`Refreshing tools from ${servers.length} running servers`);

    for (const server of servers) {
      await this.discoverToolsFromServer(server.id);
    }
  }

  private async discoverAllTools(): Promise<void> {
    const servers = this.serverRegistry.getRunningServers();

    this.logger.log(`Discovering tools from ${servers.length} running servers`);

    for (const server of servers) {
      await this.discoverToolsFromServer(server.id);
    }
  }
}
