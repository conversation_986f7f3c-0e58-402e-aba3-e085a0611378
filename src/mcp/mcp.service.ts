import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Pool, PoolClient } from 'pg';
import OpenAI from 'openai';

interface PostgreSQLTool {
  name: string;
  description: string;
  inputSchema: any;
  handler: (args: any) => Promise<string>;
}

@Injectable()
export class McpService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(McpService.name);
  private pool: Pool | null = null;
  private tools: OpenAI.Chat.Completions.ChatCompletionTool[] = [];

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    const databaseConfig = this.configService.get('database');

    if (!databaseConfig?.url) {
      this.logger.warn('No database URL configured, MCP service will not be available');
      return;
    }

    try {
      // Initialize PostgreSQL connection pool
      this.pool = new Pool({
        connectionString: databaseConfig.url,
        max: 10,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });

      // Test connection
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();

      // Initialize tools
      this.initializeTools();

      this.logger.log('MCP Service initialized successfully with PostgreSQL integration');
    } catch (error) {
      this.logger.error(`Failed to initialize MCP service: ${error.message}`, error.stack);
      this.pool = null;
    }
  }

  async onModuleDestroy() {
    if (this.pool) {
      await this.pool.end();
      this.logger.log('PostgreSQL connection pool closed');
    }
  }

  private initializeTools() {
    const postgresTools: PostgreSQLTool[] = [
      {
        name: 'read_query',
        description: 'Execute a read-only SQL query against the PostgreSQL database. Only SELECT statements are allowed.',
        inputSchema: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: 'The SQL query to execute (SELECT statements only)'
            }
          },
          required: ['query']
        },
        handler: this.executeReadQuery.bind(this)
      },
      {
        name: 'list_tables',
        description: 'List all tables and views in the database with their schemas.',
        inputSchema: {
          type: 'object',
          properties: {
            schema: {
              type: 'string',
              description: 'Optional schema name to filter tables'
            }
          }
        },
        handler: this.listTables.bind(this)
      },
      {
        name: 'describe_table',
        description: 'Get detailed information about a table including columns, types, constraints, and indexes.',
        inputSchema: {
          type: 'object',
          properties: {
            table_name: {
              type: 'string',
              description: 'Name of the table to describe'
            },
            schema: {
              type: 'string',
              description: 'Schema name (optional, defaults to public)'
            }
          },
          required: ['table_name']
        },
        handler: this.describeTable.bind(this)
      },
      {
        name: 'list_schemas',
        description: 'List all schemas in the database.',
        inputSchema: {
          type: 'object',
          properties: {}
        },
        handler: this.listSchemas.bind(this)
      }
    ];

    // Convert to OpenAI format
    this.tools = postgresTools.map(tool => ({
      type: 'function' as const,
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.inputSchema
      }
    }));

    this.logger.log(`Initialized ${this.tools.length} PostgreSQL tools: ${this.tools.map(t => t.function.name).join(', ')}`);
  }

  async getAvailableTools(): Promise<OpenAI.Chat.Completions.ChatCompletionTool[]> {
    if (!this.pool) {
      this.logger.warn('Database not available, returning empty tools list');
      return [];
    }

    this.logger.log(`Providing ${this.tools.length} available PostgreSQL tools`);
    return this.tools;
  }

  async executeTool(toolCall: OpenAI.Chat.Completions.ChatCompletionMessageToolCall): Promise<string> {
    this.logger.log(`Executing tool: ${toolCall.function.name} with arguments: ${toolCall.function.arguments}`);

    if (!this.pool) {
      return 'Database connection not available';
    }

    try {
      const startTime = Date.now();
      const args = JSON.parse(toolCall.function.arguments);

      // Find the tool handler
      const postgresTools = this.getPostgresTools();
      const tool = postgresTools.find(t => t.name === toolCall.function.name);

      if (!tool) {
        return `Unknown tool: ${toolCall.function.name}`;
      }

      const result = await tool.handler(args);
      const duration = Date.now() - startTime;

      this.logger.log(`Tool ${toolCall.function.name} executed in ${duration}ms`);
      return result;
    } catch (error) {
      this.logger.error(`Tool execution failed for ${toolCall.function.name}: ${error.message}`, error.stack);
      return `Tool execution failed: ${error.message}`;
    }
  }

  private getPostgresTools(): PostgreSQLTool[] {
    return [
      {
        name: 'read_query',
        description: 'Execute a read-only SQL query',
        inputSchema: {},
        handler: this.executeReadQuery.bind(this)
      },
      {
        name: 'list_tables',
        description: 'List tables',
        inputSchema: {},
        handler: this.listTables.bind(this)
      },
      {
        name: 'describe_table',
        description: 'Describe table',
        inputSchema: {},
        handler: this.describeTable.bind(this)
      },
      {
        name: 'list_schemas',
        description: 'List schemas',
        inputSchema: {},
        handler: this.listSchemas.bind(this)
      }
    ];
  }

  private async executeReadQuery(args: { query: string }): Promise<string> {
    const { query } = args;

    // Validate that it's a read-only query
    const trimmedQuery = query.trim().toLowerCase();
    if (!trimmedQuery.startsWith('select') && !trimmedQuery.startsWith('with')) {
      throw new Error('Only SELECT and WITH queries are allowed');
    }

    // Check for dangerous keywords
    const dangerousKeywords = ['insert', 'update', 'delete', 'drop', 'create', 'alter', 'truncate'];
    if (dangerousKeywords.some(keyword => trimmedQuery.includes(keyword))) {
      throw new Error('Query contains forbidden keywords. Only read operations are allowed.');
    }

    const client = await this.pool!.connect();
    try {
      this.logger.log(`Executing query: ${query.substring(0, 100)}...`);
      const result = await client.query(query);

      return JSON.stringify({
        rows: result.rows,
        rowCount: result.rowCount,
        fields: result.fields?.map(f => ({ name: f.name, dataTypeID: f.dataTypeID }))
      }, null, 2);
    } finally {
      client.release();
    }
  }

  private async listTables(args: { schema?: string }): Promise<string> {
    const { schema } = args;

    let query = `
      SELECT
        schemaname as schema_name,
        tablename as table_name,
        'table' as type
      FROM pg_tables
    `;

    if (schema) {
      query += ` WHERE schemaname = $1`;
    } else {
      query += ` WHERE schemaname NOT IN ('information_schema', 'pg_catalog')`;
    }

    query += `
      UNION ALL
      SELECT
        schemaname as schema_name,
        viewname as table_name,
        'view' as type
      FROM pg_views
    `;

    if (schema) {
      query += ` WHERE schemaname = $1`;
    } else {
      query += ` WHERE schemaname NOT IN ('information_schema', 'pg_catalog')`;
    }

    query += ` ORDER BY schema_name, table_name`;

    const client = await this.pool!.connect();
    try {
      const result = schema
        ? await client.query(query, [schema])
        : await client.query(query);

      return JSON.stringify({
        tables: result.rows,
        count: result.rowCount
      }, null, 2);
    } finally {
      client.release();
    }
  }

  private async describeTable(args: { table_name: string; schema?: string }): Promise<string> {
    const { table_name, schema = 'public' } = args;

    const query = `
      SELECT
        c.column_name,
        c.data_type,
        c.is_nullable,
        c.column_default,
        c.character_maximum_length,
        c.numeric_precision,
        c.numeric_scale,
        tc.constraint_type,
        kcu.constraint_name
      FROM information_schema.columns c
      LEFT JOIN information_schema.key_column_usage kcu
        ON c.table_name = kcu.table_name
        AND c.column_name = kcu.column_name
        AND c.table_schema = kcu.table_schema
      LEFT JOIN information_schema.table_constraints tc
        ON kcu.constraint_name = tc.constraint_name
        AND kcu.table_schema = tc.table_schema
      WHERE c.table_name = $1 AND c.table_schema = $2
      ORDER BY c.ordinal_position
    `;

    const client = await this.pool!.connect();
    try {
      const result = await client.query(query, [table_name, schema]);

      if (result.rows.length === 0) {
        return `Table ${schema}.${table_name} not found`;
      }

      return JSON.stringify({
        table_name: `${schema}.${table_name}`,
        columns: result.rows
      }, null, 2);
    } finally {
      client.release();
    }
  }

  private async listSchemas(): Promise<string> {
    const query = `
      SELECT schema_name
      FROM information_schema.schemata
      WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      ORDER BY schema_name
    `;

    const client = await this.pool!.connect();
    try {
      const result = await client.query(query);

      return JSON.stringify({
        schemas: result.rows.map(row => row.schema_name),
        count: result.rowCount
      }, null, 2);
    } finally {
      client.release();
    }
  }
}
